import AsyncStorage from '@react-native-async-storage/async-storage';
import { ordersAPI } from './orders';

export class StoreAPI {
  // Decodificar JWT para obtener el ID de la tienda
  async decodeJWT(): Promise<string> {
    try {
      const token = await AsyncStorage.getItem('token');
      if (!token) {
        throw new Error('No token found');
      }

      // Decodificar el JWT (parte del payload)
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      );

      const payload = JSON.parse(jsonPayload);
      console.log('🔓 JWT decodificado:', payload);
      
      // El ID puede estar en diferentes campos según tu implementación
      const storeId = payload.sub || payload.id || payload._id || payload.userId;
      
      if (!storeId) {
        throw new Error('Store ID not found in token');
      }

      console.log('🏪 Store ID obtenido:', storeId);
      return storeId;
    } catch (error) {
      console.error('❌ Error decodificando JWT:', error);
      throw error;
    }
  }

  // Obtener órdenes de la tienda
  async getOrdersByStoreId(storeId: string, page = 1, limit = 50, status?: string) {
    return ordersAPI.getOrdersByStoreId(storeId, page, limit, status);
  }

  // Obtener una orden específica
  async getOrder(orderId: string) {
    return ordersAPI.getOrder(orderId);
  }

  // Actualizar estado de una orden
  async updateOrderStatus(orderId: string, status: string) {
    return ordersAPI.updateOrderStatus(orderId, status);
  }
}

// Instancia singleton
export const storeAPI = new StoreAPI();

import { create } from 'zustand';
import { User } from '../types';
import { login as authLogin, logout as authLogout, getProfile as authGetProfile } from '../services/auth';

interface AuthState {
  user: User | null;
  isLoading: boolean;
  error: string | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  getProfile: () => Promise<void>;
}

export const useAuthStore = create<AuthState>((set) => ({
  user: null,
  isLoading: false,
  error: null,
  
  login: async (email, password) => {
    console.log('🏪 AuthStore: Iniciando proceso de login');
    console.log('📧 AuthStore: Email recibido:', email);

    set({ isLoading: true, error: null });

    try {
      console.log('🔄 AuthStore: Llamando a authLogin...');
      const user = await authLogin(email, password);

      console.log('✅ AuthStore: Login exitoso, usuario recibido:', user);
      set({ user, isLoading: false });
      console.log('🎉 AuthStore: Estado actualizado con usuario');

    } catch (error) {
      console.error('❌ AuthStore: Error en login:', error);
      console.log('💥 AuthStore: Actualizando estado con error');

      let errorMessage = 'Login failed. Please check your credentials.';

      // Personalizar mensaje según el tipo de error
      if (error.code === 'ERR_NETWORK') {
        errorMessage = 'Network error. Check your connection and API URL.';
      } else if (error.response?.status === 401) {
        errorMessage = 'Invalid credentials. Please check your email and password.';
      } else if (error.response?.status === 500) {
        errorMessage = 'Server error. Please try again later.';
      }

      set({ error: errorMessage, isLoading: false });
    }
  },
  
  logout: async () => {
    set({ isLoading: true });
    try {
      await authLogout();
      set({ user: null, isLoading: false });
    } catch (error) {
      set({ error: 'Logout failed', isLoading: false });
    }
  },

  getProfile: async () => {
    set({ isLoading: true });
    try {
      const user = await authGetProfile();
      set({ user, isLoading: false });
    } catch (error) {
      set({ error: 'Failed to fetch profile', isLoading: false });
    }
  }
}));
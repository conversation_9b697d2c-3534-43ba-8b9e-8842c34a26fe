import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Alert } from 'react-native';

// Para desarrollo en dispositivo físico, necesitas usar la IP de tu máquina
// Asegúrate de que esta IP sea accesible desde tu dispositivo
const API_URL = 'http://************:3000/api'; // IP real de tu máquina - Microservicio principal
const ORDERS_API_URL = 'http://************:3003/api/v1'; // IP real - Microservicio de órdenes

console.log('Configurando API principal con URL:', API_URL);
console.log('Configurando API de órdenes con URL:', ORDERS_API_URL);

// Cliente para el microservicio principal (auth, etc.)
export const apiClient = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 10000,
});

// Cliente para el microservicio de órdenes
export const ordersApiClient = axios.create({
  baseURL: ORDERS_API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 10000,
});

// Configurar interceptor para añadir token y manejar errores
export const setupInterceptors = async () => {
  console.log('Configurando interceptores de API');

  // Interceptores para el cliente principal
  apiClient.interceptors.request.use(
    async (config) => {
      console.log('🔐 [Principal] Interceptando solicitud a:', config.url);
      const token = await AsyncStorage.getItem('token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
        console.log('✅ [Principal] Token añadido a la solicitud');
      }
      return config;
    },
    (error) => {
      console.error('❌ [Principal] Error en interceptor de solicitud:', error);
      return Promise.reject(error);
    }
  );

  apiClient.interceptors.response.use(
    (response) => {
      console.log('✅ [Principal] Respuesta recibida de:', response.config.url);
      return response;
    },
    (error) => {
      console.error('❌ [Principal] Error en respuesta de API:', error.message);
      return Promise.reject(error);
    }
  );

  // Interceptores para el cliente de órdenes
  ordersApiClient.interceptors.request.use(
    async (config) => {
      console.log('📦 [Órdenes] Interceptando solicitud a:', config.url);
      const token = await AsyncStorage.getItem('token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
        console.log('✅ [Órdenes] Token añadido a la solicitud');
      }
      return config;
    },
    (error) => {
      console.error('❌ [Órdenes] Error en interceptor de solicitud:', error);
      return Promise.reject(error);
    }
  );

  ordersApiClient.interceptors.response.use(
    (response) => {
      console.log('✅ [Órdenes] Respuesta recibida de:', response.config.url);
      return response;
    },
    (error) => {
      console.error('❌ [Órdenes] Error en respuesta de API:', error.message);

      if (error.code === 'ECONNABORTED') {
        Alert.alert('Timeout Error', 'La solicitud tardó demasiado. Verifica tu conexión.');
      }

      if (error.code === 'ERR_NETWORK') {
        Alert.alert(
          'Error de Red',
          'No se pudo conectar al servidor de órdenes. Verifica:\n' +
          '1. Que el microservicio de órdenes esté corriendo en el puerto 3003\n' +
          '2. Que tu dispositivo y servidor estén en la misma red\n' +
          '3. Que no haya firewall bloqueando la conexión'
        );
      }

      return Promise.reject(error);
    }
  );
};

// Función para probar la conexión a la API
export const testApiConnection = async () => {
  try {
    console.log('Probando conexión a:', API_URL);
    console.log('Configuración de red:');
    console.log('- IP del servidor: ************');
    console.log('- Puerto: 3000');
    console.log('- URL completa:', API_URL);

    // Intentamos con diferentes endpoints que podrían existir
    // Primero probamos con la raíz del API
    const response = await apiClient.get('/', { timeout: 5000 });
    console.log('Conexión exitosa:', response.status);
    console.log('Respuesta del servidor:', response.data);
    Alert.alert('Conexión Exitosa', `API respondió con estado: ${response.status}`);
    return true;
  } catch (error) {
    console.error('Error al probar conexión:', error.message);

    // Información más detallada del error
    let errorMessage = `No se pudo conectar a ${API_URL}\n`;

    if (error.code === 'ECONNREFUSED') {
      errorMessage += 'Error: Conexión rechazada. Verifica que:\n';
      errorMessage += '1. El servidor esté ejecutándose en el puerto 3000\n';
      errorMessage += '2. No haya firewall bloqueando la conexión\n';
      errorMessage += '3. Tu dispositivo esté en la misma red WiFi';
    } else if (error.code === 'ENOTFOUND') {
      errorMessage += 'Error: No se pudo resolver la dirección IP\n';
      errorMessage += 'Verifica que la IP ************ sea correcta';
    } else {
      errorMessage += `Error: ${error.message}`;
    }

    Alert.alert('Error de Conexión', errorMessage);
    return false;
  }
};


import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Alert } from 'react-native';

// Para desarrollo en dispositivo físico, necesitas usar la IP de tu máquina
// Asegúrate de que esta IP sea accesible desde tu dispositivo
const API_URL = 'http://192.168.1.X:3000/api'; // Reemplaza con tu IP real
console.log('Configurando API con URL:', API_URL);

export const apiClient = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  // Aumentar el timeout para dar más tiempo a la respuesta
  timeout: 10000,
});

// Configurar interceptor para añadir token y manejar errores
export const setupInterceptors = async () => {
  console.log('Configurando interceptores de API');
  
  apiClient.interceptors.request.use(
    async (config) => {
      console.log('Interceptando solicitud a:', config.url);
      const token = await AsyncStorage.getItem('token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
        console.log('Token añadido a la solicitud');
      }
      return config;
    },
    (error) => {
      console.error('Error en interceptor de solicitud:', error);
      return Promise.reject(error);
    }
  );

  apiClient.interceptors.response.use(
    (response) => {
      console.log('Respuesta recibida de:', response.config.url);
      return response;
    },
    (error) => {
      console.error('Error en respuesta de API:', error.message);
      
      if (error.code === 'ECONNABORTED') {
        Alert.alert('Timeout Error', 'La solicitud tardó demasiado. Verifica tu conexión.');
      }
      
      if (error.code === 'ERR_NETWORK') {
        Alert.alert(
          'Error de Red', 
          'No se pudo conectar al servidor. Verifica:\n' +
          '1. Que la URL del API sea correcta\n' +
          '2. Que tu dispositivo y servidor estén en la misma red\n' +
          '3. Que no haya firewall bloqueando la conexión'
        );
      }
      
      return Promise.reject(error);
    }
  );
};

// Función para probar la conexión a la API
export const testApiConnection = async () => {
  try {
    console.log('Probando conexión a:', API_URL);
    const response = await apiClient.get('/health');
    console.log('Conexión exitosa:', response.status);
    Alert.alert('Conexión Exitosa', `API respondió con estado: ${response.status}`);
    return true;
  } catch (error) {
    console.error('Error al probar conexión:', error.message);
    Alert.alert(
      'Error de Conexión', 
      `No se pudo conectar a ${API_URL}\n` +
      `Error: ${error.message}`
    );
    return false;
  }
};


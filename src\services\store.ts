import { apiClient } from './api';
import { storeAPI } from './storeAPI';
import { Product, Order } from '../types';

interface DashboardStats {
  totalSales: number;
  pendingOrders: number;
  completedOrders: number;
  revenue: {
    daily: number;
    weekly: number;
    monthly: number;
  };
}

export const storeService = {
  async getProducts(): Promise<Product[]> {
    console.log('🛍️ Obteniendo productos...');
    try {
      const response = await apiClient.get<Product[]>('/products');
      console.log('✅ Productos obtenidos:', response.data);
      return response.data;
    } catch (error) {
      console.log('⚠️ Endpoint /products no existe, usando datos mock');
      // Datos mock mientras se implementa el endpoint
      return [
        {
          id: '1',
          name: 'Pizza Margherita',
          description: 'Pizza clásica con tomate, mozzarella y albahaca',
          price: 12.99,
          stock: 25,
          category: 'Pizza',
          image: 'https://via.placeholder.com/150'
        },
        {
          id: '2',
          name: 'Hamburguesa Clásica',
          description: 'Hamburguesa con carne, lechuga, tomate y queso',
          price: 8.50,
          stock: 15,
          category: 'Hamburguesas',
          image: 'https://via.placeholder.com/150'
        }
      ];
    }
  },

  async createProduct(product: Omit<Product, 'id'>): Promise<Product> {
    console.log('➕ Creando producto:', product);
    const response = await apiClient.post<Product>('/products', product);
    console.log('✅ Producto creado:', response.data);
    return response.data;
  },

  async updateProduct(id: string, product: Partial<Product>): Promise<Product> {
    console.log('📝 Actualizando producto:', id, product);
    const response = await apiClient.put<Product>(`/products/${id}`, product);
    console.log('✅ Producto actualizado:', response.data);
    return response.data;
  },

  async getOrders(): Promise<Order[]> {
    console.log('📋 Obteniendo órdenes de la tienda...');
    try {
      // Obtener el ID de la tienda desde el JWT
      const storeId = await storeAPI.decodeJWT();

      // Obtener órdenes usando el microservicio de órdenes
      const response = await storeAPI.getOrdersByStoreId(storeId);

      // Transformar los datos al formato esperado
      const orders: Order[] = response.data.map((order: any) => ({
        id: order.id || order._id,
        items: order.items || [],
        customer: {
          name: order.customerName || order.customer?.name || 'Cliente Desconocido',
          address: order.customerAddress || order.customer?.address || '',
          phone: order.customerPhone || order.customer?.phone || ''
        },
        total: order.total || 0,
        status: order.status || 'PENDING',
        deliveryMethod: order.deliveryMethod || 'PICKUP',
        createdAt: order.createdAt || new Date().toISOString()
      }));

      console.log('✅ Órdenes transformadas:', orders);
      return orders;
    } catch (error) {
      console.error('❌ Error obteniendo órdenes:', error);
      // Retornar array vacío en caso de error
      return [];
    }
  },

  async updateOrderStatus(id: string, status: string): Promise<Order> {
    console.log('🔄 Actualizando estado de orden:', id, 'a', status);
    try {
      const response = await storeAPI.updateOrderStatus(id, status);
      console.log('✅ Estado de orden actualizado:', response);

      // Transformar la respuesta al formato esperado
      return {
        id: response.id || response._id,
        items: response.items || [],
        customer: {
          name: response.customerName || response.customer?.name || 'Cliente Desconocido',
          address: response.customerAddress || response.customer?.address || '',
          phone: response.customerPhone || response.customer?.phone || ''
        },
        total: response.total || 0,
        status: response.status || status,
        deliveryMethod: response.deliveryMethod || 'PICKUP',
        createdAt: response.createdAt || new Date().toISOString()
      };
    } catch (error) {
      console.error('❌ Error actualizando estado de orden:', error);
      throw error;
    }
  },

  async getDashboardStats(): Promise<DashboardStats> {
    console.log('📊 Obteniendo estadísticas del dashboard...');
    const response = await apiClient.get<DashboardStats>('/dashboard');
    console.log('✅ Estadísticas obtenidas:', response.data);
    return response.data;
  }
};
import { apiClient } from './api';
import { Product, Order } from '../types';

interface DashboardStats {
  totalSales: number;
  pendingOrders: number;
  completedOrders: number;
  revenue: {
    daily: number;
    weekly: number;
    monthly: number;
  };
}

export const storeService = {
  async getProducts(): Promise<Product[]> {
    console.log('🛍️ Obteniendo productos...');
    try {
      const response = await apiClient.get<Product[]>('/products');
      console.log('✅ Productos obtenidos:', response.data);
      return response.data;
    } catch (error) {
      console.log('⚠️ Endpoint /products no existe, usando datos mock');
      // Datos mock mientras se implementa el endpoint
      return [
        {
          id: '1',
          name: 'Pizza Margherita',
          description: 'Pizza clásica con tomate, mozzarella y albahaca',
          price: 12.99,
          stock: 25,
          category: 'Pizza',
          image: 'https://via.placeholder.com/150'
        },
        {
          id: '2',
          name: 'Hamburguesa Clásica',
          description: 'Hamburguesa con carne, lechuga, tomate y queso',
          price: 8.50,
          stock: 15,
          category: 'Hamburguesas',
          image: 'https://via.placeholder.com/150'
        }
      ];
    }
  },

  async createProduct(product: Omit<Product, 'id'>): Promise<Product> {
    console.log('➕ Creando producto:', product);
    const response = await apiClient.post<Product>('/products', product);
    console.log('✅ Producto creado:', response.data);
    return response.data;
  },

  async updateProduct(id: string, product: Partial<Product>): Promise<Product> {
    console.log('📝 Actualizando producto:', id, product);
    const response = await apiClient.put<Product>(`/products/${id}`, product);
    console.log('✅ Producto actualizado:', response.data);
    return response.data;
  },

  async getOrders(): Promise<Order[]> {
    console.log('📋 Obteniendo órdenes...');
    const response = await apiClient.get<Order[]>('/orders/store');
    console.log('✅ Órdenes obtenidas:', response.data);
    return response.data;
  },

  async updateOrderStatus(id: string, status: string): Promise<Order> {
    console.log('🔄 Actualizando estado de orden:', id, 'a', status);
    const response = await apiClient.put<Order>(`/orders/${id}/status`, { status });
    console.log('✅ Estado de orden actualizado:', response.data);
    return response.data;
  },

  async getDashboardStats(): Promise<DashboardStats> {
    console.log('📊 Obteniendo estadísticas del dashboard...');
    const response = await apiClient.get<DashboardStats>('/dashboard');
    console.log('✅ Estadísticas obtenidas:', response.data);
    return response.data;
  }
};
import AsyncStorage from '@react-native-async-storage/async-storage';
import { ordersApiClient } from './api';
import { Order } from '../types';

interface OrdersResponse {
  data: any[];
  pagination: any;
}

export class OrdersAPI {
  private async getHeaders() {
    const headers: any = {
      'Content-Type': 'application/json',
    };

    // Obtener el user ID del JWT para el header x-user-id
    try {
      const token = await AsyncStorage.getItem('token');
      if (token) {
        const base64Url = token.split('.')[1];
        const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
        const jsonPayload = decodeURIComponent(
          atob(base64)
            .split('')
            .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
            .join('')
        );
        const payload = JSON.parse(jsonPayload);
        const userId = payload.sub || payload.id || payload._id || payload.userId;

        if (userId) {
          headers['x-user-id'] = userId;
          console.log('🆔 Header x-user-id agregado:', userId);
        }
      }
    } catch (error) {
      console.error('❌ Error obteniendo user ID para headers:', error);
    }

    return headers;
  }

  // Obtener órdenes por ID de tienda
  async getOrdersByStoreId(storeId: string, page = 1, limit = 10, status?: string): Promise<OrdersResponse> {
    try {
      console.log(`📦 Obteniendo órdenes para tienda ${storeId}...`);

      // Construir parámetros de consulta
      const params: any = { page, limit };
      if (status) {
        params.status = status;
      }

      console.log('📋 Parámetros de consulta:', params);
      console.log('🔗 URL completa:', `${ordersApiClient.defaults.baseURL}/orders/store/${storeId}`);

      const response = await ordersApiClient.get(`/orders/store/${storeId}`, {
        headers: await this.getHeaders(),
        params
      });

      console.log('✅ Órdenes obtenidas:', response.data);
      return response.data;
    } catch (error) {
      console.error(`❌ Error fetching orders for store ${storeId}:`, error);

      // Log más detallado del error
      if (error.response) {
        console.error('📡 Error response status:', error.response.status);
        console.error('📡 Error response data:', error.response.data);
        console.error('📡 Error response headers:', error.response.headers);
      } else if (error.request) {
        console.error('🌐 No response received:', error.request);
      } else {
        console.error('⚠️ Error message:', error.message);
      }

      throw error;
    }
  }

  // Obtener una orden específica
  async getOrder(orderId: string): Promise<any> {
    try {
      console.log(`📋 Obteniendo orden ${orderId}...`);
      
      const response = await ordersApiClient.get(`/orders/${orderId}`, {
        headers: await this.getHeaders(),
      });
      
      console.log('✅ Orden obtenida:', response.data);
      return response.data;
    } catch (error) {
      console.error(`❌ Error fetching order ${orderId}:`, error);
      throw error;
    }
  }

  // Actualizar estado de una orden
  async updateOrderStatus(orderId: string, status: string): Promise<any> {
    try {
      console.log(`🔄 Actualizando orden ${orderId} a estado ${status}...`);
      
      const response = await ordersApiClient.put(`/orders/${orderId}/status`,
        { status },
        { headers: await this.getHeaders() }
      );
      
      console.log('✅ Estado de orden actualizado:', response.data);
      return response.data;
    } catch (error) {
      console.error(`❌ Error updating order ${orderId} status:`, error);
      throw error;
    }
  }

  // Obtener órdenes disponibles para delivery
  async getAvailableOrders(): Promise<Order[]> {
    try {
      console.log('🚚 Obteniendo órdenes disponibles para delivery...');
      
      const response = await ordersApiClient.get('/orders/delivery/available', {
        headers: await this.getHeaders(),
      });
      
      console.log('✅ Órdenes disponibles obtenidas:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Error fetching available orders:', error);
      throw error;
    }
  }

  // Aceptar una orden (para delivery)
  async acceptOrder(orderId: string): Promise<Order> {
    try {
      console.log(`✋ Aceptando orden ${orderId}...`);
      
      const response = await ordersApiClient.post(`/orders/${orderId}/accept`, {}, {
        headers: await this.getHeaders(),
      });
      
      console.log('✅ Orden aceptada:', response.data);
      return response.data;
    } catch (error) {
      console.error(`❌ Error accepting order ${orderId}:`, error);
      throw error;
    }
  }

  // Obtener mis órdenes (para delivery)
  async getMyOrders(): Promise<Order[]> {
    try {
      console.log('📦 Obteniendo mis órdenes...');
      
      const response = await ordersApiClient.get('/orders/delivery/my-orders', {
        headers: await this.getHeaders(),
      });
      
      console.log('✅ Mis órdenes obtenidas:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Error fetching my orders:', error);
      throw error;
    }
  }
}

// Instancia singleton
export const ordersAPI = new OrdersAPI();

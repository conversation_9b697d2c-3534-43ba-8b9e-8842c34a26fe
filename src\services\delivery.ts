import { ordersAPI } from './orders';
import { Order } from '../types';

interface DeliveryStats {
  totalDeliveries: number;
  earnings: number;
  rating: number;
}

export const deliveryService = {
  async getAvailableOrders(): Promise<Order[]> {
    return ordersAPI.getAvailableOrders();
  },

  async acceptOrder(id: string): Promise<Order> {
    return ordersAPI.acceptOrder(id);
  },

  async getMyOrders(): Promise<Order[]> {
    return ordersAPI.getMyOrders();
  },

  async updateOrderStatus(id: string, status: string): Promise<Order> {
    console.log('🔄 Actualizando estado de orden delivery:', id, 'a', status);
    const response = await ordersAPI.updateOrderStatus(id, status);

    // Transformar la respuesta al formato esperado
    return {
      id: response.id || response._id,
      items: response.items || [],
      customer: {
        name: response.customerName || response.customer?.name || 'Cliente Desconocido',
        address: response.customerAddress || response.customer?.address || '',
        phone: response.customerPhone || response.customer?.phone || ''
      },
      total: response.total || 0,
      status: response.status || status,
      deliveryMethod: response.deliveryMethod || 'DELIVERY',
      createdAt: response.createdAt || new Date().toISOString()
    };
  },

  async getStats(): Promise<DeliveryStats> {
    console.log('📊 Obteniendo estadísticas de delivery...');
    // Por ahora retornamos datos mock ya que este endpoint probablemente esté en otro microservicio
    return {
      totalDeliveries: 0,
      earnings: 0,
      rating: 0
    };
  }
};
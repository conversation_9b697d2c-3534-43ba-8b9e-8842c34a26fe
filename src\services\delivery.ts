import { apiClient } from './api';
import { Order } from '../types';

interface DeliveryStats {
  totalDeliveries: number;
  earnings: number;
  rating: number;
}

export const deliveryService = {
  async getAvailableOrders(): Promise<Order[]> {
    console.log('🚚 Obteniendo órdenes disponibles...');
    const response = await apiClient.get<Order[]>('/orders/delivery/available');
    console.log('✅ Órdenes disponibles obtenidas:', response.data);
    return response.data;
  },

  async acceptOrder(id: string): Promise<Order> {
    console.log('✋ Aceptando orden:', id);
    const response = await apiClient.post<Order>(`/orders/${id}/accept`);
    console.log('✅ Orden aceptada:', response.data);
    return response.data;
  },

  async getMyOrders(): Promise<Order[]> {
    console.log('📦 Obteniendo mis órdenes...');
    const response = await apiClient.get<Order[]>('/orders/delivery/my-orders');
    console.log('✅ Mis órdenes obtenidas:', response.data);
    return response.data;
  },

  async updateOrderStatus(id: string, status: string): Promise<Order> {
    console.log('🔄 Actualizando estado de orden delivery:', id, 'a', status);
    const response = await apiClient.put<Order>(`/orders/${id}/status`, { status });
    console.log('✅ Estado de orden delivery actualizado:', response.data);
    return response.data;
  },

  async getStats(): Promise<DeliveryStats> {
    console.log('📊 Obteniendo estadísticas de delivery...');
    const response = await apiClient.get<DeliveryStats>('/delivery/stats');
    console.log('✅ Estadísticas de delivery obtenidas:', response.data);
    return response.data;
  }
};
import AsyncStorage from '@react-native-async-storage/async-storage';
import { apiClient } from './api';
import { Alert } from 'react-native';

interface User {
  id: string;
  email: string;
  name: string;
  role: string;
}

interface LoginResponse {
  access_token: string;
  user: User;
}

export const login = async (email: string, password: string) => {
  console.log('Attempting login with:', email);
  try {
    // Mostrar la URL completa para verificar
    console.log('API URL:', apiClient.defaults.baseURL + '/auth/login');
    
    const response = await apiClient.post<LoginResponse>('/auth/login', { 
      email, 
      password 
    });
    
    console.log('Login response status:', response.status);
    console.log('Login response data:', response.data);
    
    // Guardar token en AsyncStorage (equivalente a localStorage)
    const { access_token, user } = response.data;
    await AsyncStorage.setItem('token', access_token);
    await AsyncStorage.setItem('user', JSON.stringify(user));
    
    // Configurar token para futuras solicitudes
    apiClient.defaults.headers.common['Authorization'] = `Bearer ${access_token}`;
    
    console.log('Login successful');
    return user;
  } catch (error) {
    console.error('Login error type:', typeof error);
    console.error('Login error message:', error.message);
    
    // Mostrar alerta con detalles del error
    Alert.alert(
      'Login Error Details',
      `Error: ${error.message}\n` +
      `Status: ${error.response?.status || 'N/A'}\n` +
      `Data: ${JSON.stringify(error.response?.data || {})}`
    );
    
    if (error.response) {
      console.error('Error status:', error.response.status);
      console.error('Error data:', error.response.data);
    } else if (error.request) {
      // La solicitud se hizo pero no se recibió respuesta
      console.error('No response received:', error.request);
      Alert.alert('Network Error', 'No response received from server. Check your API URL and network connection.');
    } else {
      // Error al configurar la solicitud
      console.error('Error setting up request:', error.message);
    }
    throw error;
  }
};

export const logout = async () => {
  await AsyncStorage.removeItem('token');
  await AsyncStorage.removeItem('user');
  delete apiClient.defaults.headers.common['Authorization'];
};

export const getProfile = async () => {
  const userJson = await AsyncStorage.getItem('user');
  if (userJson) {
    return JSON.parse(userJson);
  }
  return null;
};


